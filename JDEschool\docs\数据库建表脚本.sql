+-- JDEschool 校园代取快递小程序数据库建表脚本
-- 数据库版本: MySQL 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS jdeschool 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jdeschool;

-- 1. 用户表
CREATE TABLE users (
    user_id VARCHAR(32) PRIMARY KEY COMMENT '用户ID（主键）',
    openid VARCHAR(64) NOT NULL UNIQUE COMMENT '微信openid',
    union_id VARCHAR(64) COMMENT '微信unionid',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(11) COMMENT '手机号',
    student_id VARCHAR(20) COMMENT '学号',
    real_name VARCHAR(20) COMMENT '真实姓名',
    college VARCHAR(50) COMMENT '学院',
    dormitory VARCHAR(50) COMMENT '宿舍',
    credit_score INT DEFAULT 100 COMMENT '信用分（默认100）',
    is_courier TINYINT(1) DEFAULT 0 COMMENT '是否为代取员（0-否，1-是）',
    courier_status TINYINT(1) DEFAULT 0 COMMENT '代取员状态（0-未申请，1-审核中，2-已通过，3-已拒绝）',
    status TINYINT(1) DEFAULT 1 COMMENT '账户状态（0-禁用，1-正常）',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_openid (openid),
    INDEX idx_student_id (student_id),
    INDEX idx_phone (phone),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 地址表
CREATE TABLE addresses (
    address_id VARCHAR(32) PRIMARY KEY COMMENT '地址ID（主键）',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    name VARCHAR(20) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(11) NOT NULL COMMENT '联系电话',
    location VARCHAR(200) NOT NULL COMMENT '详细地址',
    latitude DECIMAL(10,7) COMMENT '纬度',
    longitude DECIMAL(10,7) COMMENT '经度',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认地址（0-否，1-是）',
    type VARCHAR(20) DEFAULT '宿舍' COMMENT '地址类型（宿舍/教学楼/其他）',
    status TINYINT(1) DEFAULT 1 COMMENT '状态（0-删除，1-正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地址表';

-- 3. 订单表
CREATE TABLE orders (
    order_id VARCHAR(32) PRIMARY KEY COMMENT '订单ID（主键）',
    order_no VARCHAR(20) NOT NULL UNIQUE COMMENT '订单编号',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    courier_id VARCHAR(32) COMMENT '代取员ID',
    express_company VARCHAR(20) NOT NULL COMMENT '快递公司',
    pickup_code VARCHAR(50) NOT NULL COMMENT '取件码',
    pickup_location JSON NOT NULL COMMENT '取件地点（JSON格式：{name, address, latitude, longitude}）',
    delivery_location JSON NOT NULL COMMENT '送达地点（JSON格式：{name, address, latitude, longitude}）',
    item_description VARCHAR(200) COMMENT '物品描述',
    delivery_fee DECIMAL(8,2) NOT NULL COMMENT '代取费用',
    platform_fee DECIMAL(8,2) DEFAULT 0.00 COMMENT '平台服务费',
    total_fee DECIMAL(8,2) NOT NULL COMMENT '总费用',
    status TINYINT(2) DEFAULT 1 COMMENT '订单状态（1-待接单，2-已接单，3-取件中，4-配送中，5-待确认，6-已完成，7-已取消）',
    special_requirements VARCHAR(500) COMMENT '特殊要求',
    cancel_reason VARCHAR(200) COMMENT '取消原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    accept_time DATETIME COMMENT '接单时间',
    pickup_time DATETIME COMMENT '取件时间',
    delivery_time DATETIME COMMENT '送达时间',
    complete_time DATETIME COMMENT '完成时间',
    cancel_time DATETIME COMMENT '取消时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (courier_id) REFERENCES users(user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_courier_id (courier_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_express_company (express_company)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 4. 订单状态变更记录表
CREATE TABLE order_status_logs (
    log_id VARCHAR(32) PRIMARY KEY COMMENT '日志ID',
    order_id VARCHAR(32) NOT NULL COMMENT '订单ID',
    from_status TINYINT(2) COMMENT '原状态',
    to_status TINYINT(2) NOT NULL COMMENT '新状态',
    operator_id VARCHAR(32) COMMENT '操作人ID',
    operator_type TINYINT(1) NOT NULL COMMENT '操作人类型（1-用户，2-代取员，3-系统）',
    remark VARCHAR(200) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单状态变更记录表';

-- 5. 支付记录表
CREATE TABLE payments (
    payment_id VARCHAR(32) PRIMARY KEY COMMENT '支付ID',
    order_id VARCHAR(32) NOT NULL COMMENT '订单ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    payment_no VARCHAR(32) NOT NULL UNIQUE COMMENT '支付单号',
    third_party_no VARCHAR(64) COMMENT '第三方支付单号',
    payment_method TINYINT(1) NOT NULL COMMENT '支付方式（1-微信支付，2-余额支付）',
    amount DECIMAL(8,2) NOT NULL COMMENT '支付金额',
    status TINYINT(1) DEFAULT 1 COMMENT '支付状态（1-待支付，2-支付成功，3-支付失败，4-已退款）',
    pay_time DATETIME COMMENT '支付时间',
    refund_time DATETIME COMMENT '退款时间',
    refund_reason VARCHAR(200) COMMENT '退款原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_no (payment_no),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- 6. 评价表
CREATE TABLE reviews (
    review_id VARCHAR(32) PRIMARY KEY COMMENT '评价ID',
    order_id VARCHAR(32) NOT NULL COMMENT '订单ID',
    reviewer_id VARCHAR(32) NOT NULL COMMENT '评价人ID',
    reviewee_id VARCHAR(32) NOT NULL COMMENT '被评价人ID',
    service_rating TINYINT(1) NOT NULL COMMENT '服务态度评分（1-5分）',
    speed_rating TINYINT(1) NOT NULL COMMENT '配送速度评分（1-5分）',
    integrity_rating TINYINT(1) NOT NULL COMMENT '物品完整性评分（1-5分）',
    overall_rating DECIMAL(2,1) NOT NULL COMMENT '综合评分',
    comment VARCHAR(500) COMMENT '评价内容',
    is_anonymous TINYINT(1) DEFAULT 0 COMMENT '是否匿名（0-否，1-是）',
    status TINYINT(1) DEFAULT 1 COMMENT '状态（0-删除，1-正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (reviewer_id) REFERENCES users(user_id),
    FOREIGN KEY (reviewee_id) REFERENCES users(user_id),
    UNIQUE KEY uk_order_reviewer (order_id, reviewer_id),
    INDEX idx_reviewee_id (reviewee_id),
    INDEX idx_overall_rating (overall_rating),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价表';

-- 7. 消息通知表
CREATE TABLE notifications (
    notification_id VARCHAR(32) PRIMARY KEY COMMENT '通知ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    type TINYINT(2) NOT NULL COMMENT '通知类型（1-订单通知，2-系统通知，3-活动通知）',
    title VARCHAR(100) NOT NULL COMMENT '通知标题',
    content VARCHAR(500) NOT NULL COMMENT '通知内容',
    related_id VARCHAR(32) COMMENT '关联ID（如订单ID）',
    is_read TINYINT(1) DEFAULT 0 COMMENT '是否已读（0-未读，1-已读）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_time DATETIME COMMENT '阅读时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息通知表';

-- 8. 系统配置表
CREATE TABLE system_configs (
    config_id VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型（string/number/boolean/json）',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否公开（0-否，1-是）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始系统配置
INSERT INTO system_configs (config_id, config_key, config_value, description, type, is_public) VALUES
('cfg001', 'platform_fee_rate', '0.05', '平台服务费率', 'number', 1),
('cfg002', 'min_delivery_fee', '2.00', '最低代取费用', 'number', 1),
('cfg003', 'max_delivery_fee', '20.00', '最高代取费用', 'number', 1),
('cfg004', 'default_credit_score', '100', '默认信用分', 'number', 0),
('cfg005', 'courier_apply_threshold', '80', '代取员申请信用分门槛', 'number', 0);

-- 创建订单编号生成函数（可选）
-- 注意：这个函数需要在MySQL中手动创建，或者在应用层生成订单号
