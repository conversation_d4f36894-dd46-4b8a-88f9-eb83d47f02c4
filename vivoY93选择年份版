#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于uiautomator2的自动化工具
针对日常劳动上报等功能，提供更可靠的UI自动化

使用方法:
1. 安装必要的包: pip install uiautomator2 weditor
2. 初始化设备: python -m uiautomator2 init
3. 连接安卓设备并开启USB调试
4. 运行此脚本，按照提示进行操作
"""

import uiautomator2 as u2
import time
import os
import random
from datetime import datetime
import traceback

# 全局变量用于存储上次设置的时间
last_set_time = {
    "年": None,
    "月": None,
    "日": None
}

# 定义每种劳动类型需要新增的次数
# 设置为0表示不需要新增该类型
TARGET_REGISTRATIONS = {
    "室内卫生": 35,
    "室外卫生": 14,
    "宿舍值日": 43,
    "家庭劳动": 8,
    "临时性劳动": 5
}

# 记录已完成的新增登记次数
COMPLETED_REGISTRATIONS = {
    "室内卫生": 0,
    "室外卫生": 0,
    "宿舍值日": 0,
    "家庭劳动": 0,
    "临时性劳动": 0
}

try:
    from config import CONFIG
except ImportError:
    # 如果没有config.py，使用默认配置
    CONFIG = {
        "app_package": "com.supwisdom.sqgxyapp",
        "device_id": "d54c9a46"
    }


class UIAutomator2Helper:
    """基于uiautomator2的自动化工具"""

    def __init__(self, device_id=None):
        """初始化uiautomator2连接和设置目录"""
        self.device_id = device_id or CONFIG.get("device_id")
        self.package_name = CONFIG.get("app_package")

        # 尝试连接设备
        try:
            if self.device_id:
                print(f"尝试连接设备: {self.device_id}")
                self.device = u2.connect(self.device_id)
            else:
                print("尝试连接默认设备")
                self.device = u2.connect()

            print("设备连接成功")
            self.device.implicitly_wait(5.0)  # 将隐式等待从10秒减少到5秒

            # 获取设备信息
            self.device_info = self.device.info
            print(f"设备信息: {self.device_info.get('productName')} - Android {self.device_info.get('version')}")

        except Exception as e:
            print(f"设备连接失败: {e}")
            print("请确保:")
            print("1. 设备已通过USB连接")
            print("2. 已启用USB调试")
            raise

        # 设置目录结构
        self.setup_directories()

    def setup_directories(self):
        """设置必要的目录"""
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.screenshots_dir = os.path.join(self.base_dir, "screenshots")
        self.ui_dumps_dir = os.path.join(self.base_dir, "ui_dumps")

        os.makedirs(self.screenshots_dir, exist_ok=True)
        os.makedirs(self.ui_dumps_dir, exist_ok=True)

    def start_app(self):
        """启动应用"""
        print(f"启动应用: {self.package_name}")
        try:
            self.device.app_start(self.package_name)
            time.sleep(1.5)  # 等待应用启动
            print("应用启动成功")
            return True
        except Exception as e:
            print(f"应用启动失败: {e}")
            return False

    def take_screenshot(self, name=None):
        """截取屏幕并保存"""
        # 如果是劳动时间相关的截图，才执行实际的截图操作
        if name and ("time_picker" in name or "时间" in name):
            if name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"screenshot_{timestamp}.png"

            filename = os.path.join(self.screenshots_dir, name)

            try:
                self.device.screenshot(filename)
                print(f"截图已保存: {filename}")
                return filename
            except Exception as e:
                print(f"截图失败: {e}")
                return None
        else:
            # 对于非劳动时间相关的截图，仅打印日志不执行截图
            if name:
                print(f"截图已禁用: {name}")
            return None

    def save_ui_dump(self, filename=None):
        """保存当前UI结构到XML文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.ui_dumps_dir, f"uidump_{timestamp}.xml")

        try:
            xml_content = self.device.dump_hierarchy()
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            print(f"UI结构已保存: {filename}")
            return filename
        except Exception as e:
            print(f"保存UI结构失败: {e}")
            traceback.print_exc()
            return None

    def find_element_by_text(self, text, partial_match=True, timeout=10):
        """通过文本查找元素"""
        print(f"查找文本{'包含' if partial_match else '等于'}: '{text}'")
        try:
            if partial_match:
                element = self.device(textContains=text)
            else:
                element = self.device(text=text)

            if element.exists(timeout=timeout):
                print(f"找到元素: {text}")
                return element
            else:
                print(f"未找到元素: {text}")
                return None
        except Exception as e:
            print(f"查找元素异常: {e}")
            return None

    def find_element_by_id(self, resource_id, timeout=10):
        """通过资源ID查找元素"""
        print(f"查找ID: '{resource_id}'")
        try:
            element = self.device(resourceId=resource_id)
            if element.exists(timeout=timeout):
                print(f"找到元素ID: {resource_id}")
                return element
            else:
                print(f"未找到元素ID: {resource_id}")
                return None
        except Exception as e:
            print(f"查找元素ID异常: {e}")
            return None

    def tap(self, element=None, x=None, y=None):
        """点击元素或坐标"""
        try:
            if element is not None:
                if element.exists:
                    info = element.info
                    print(f"点击元素: {info.get('text', '')} {info.get('resourceId', '')}")
                    element.click()
                    return True
                else:
                    print("元素不存在，无法点击")
                    return False
            elif x is not None and y is not None:
                print(f"点击坐标: ({x}, {y})")
                self.device.click(x, y)
                return True
            else:
                print("没有提供有效的元素或坐标")
                return False
        except Exception as e:
            print(f"点击操作异常: {e}")
            return False

    def tap_element(self, identifier, by="text", partial_match=True, timeout=10):
        """点击指定特征的元素"""
        print(f"尝试点击 {by}={identifier} 的元素")

        element = None
        if by == "text":
            element = self.find_element_by_text(identifier, partial_match, timeout)
        elif by == "id":
            element = self.find_element_by_id(identifier, timeout)

        if element:
            return self.tap(element=element)
        return False

    def swipe(self, direction, scale=0.5):
        """
        滑动屏幕
        direction: 'up', 'down', 'left', 'right'
        scale: 滑动距离占屏幕的比例(0-1)
        """
        width, height = self.device.window_size()

        start_x, start_y = width // 2, height // 2
        offset = int(min(width, height) * scale)

        if direction == 'up':
            print("向上滑动")
            self.device.swipe(start_x, start_y, start_x, start_y - offset)
        elif direction == 'down':
            print("向下滑动")
            self.device.swipe(start_x, start_y, start_x, start_y + offset)
        elif direction == 'left':
            print("向左滑动")
            self.device.swipe(start_x, start_y, start_x - offset, start_y)
        elif direction == 'right':
            print("向右滑动")
            self.device.swipe(start_x, start_y, start_x + offset, start_y)
        else:
            print(f"无效的滑动方向: {direction}")
            return False

        time.sleep(1.0)  # 等待滑动响应
        return True

    def input_text(self, text, element=None, clear=True):
        """输入文本到指定元素，如未指定则输入到当前焦点"""
        try:
            if element is not None:
                if clear:
                    element.clear_text()
                print(f"向元素输入文本: {text}")
                element.set_text(text)
            else:
                print(f"向当前焦点输入文本: {text}")
                self.device.send_keys(text)
            return True
        except Exception as e:
            print(f"输入文本异常: {e}")
            return False

    def safe_input_text(self, text, x=None, y=None, element=None, max_retries=3):
        """
        增强版输入文本函数，应对各种输入异常情况

        Args:
            text: 要输入的文本
            x, y: 如果提供，在尝试输入前会先点击该坐标
            element: 要输入文本的UI元素
            max_retries: 最大重试次数

        Returns:
            bool: 是否成功输入文本
        """
        print(f"安全输入文本: {text}")

        for attempt in range(max_retries):
            # 检查是否有弹窗并处理
            if self.check_and_dismiss_popups():
                print(f"尝试 {attempt + 1}/{max_retries}: 检测到弹窗并已关闭")

            # 如果提供了坐标，先点击坐标来获取焦点
            if x is not None and y is not None:
                print(f"点击坐标 ({x}, {y}) 获取焦点")
                self.tap(x=x, y=y)
                time.sleep(1.0)

            # 尝试不同的输入方法
            try:
                if element is not None:
                    print(f"使用元素.set_text方法输入")
                    element.set_text(text)
                    return True
                else:
                    # 方法1: 使用推荐的focused=True方法
                    try:
                        print("尝试方法1: 使用focused=True")
                        focused_element = self.device(focused=True)
                        if focused_element.exists:
                            focused_element.set_text(text)
                            print("方法1成功")
                            return True
                    except Exception as e1:
                        print(f"方法1失败: {e1}")

                    # 方法2: 使用send_keys
                    try:
                        print("尝试方法2: 使用send_keys")
                        self.device.send_keys(text)
                        print("方法2成功")
                        return True
                    except Exception as e2:
                        print(f"方法2失败: {e2}")

                    # 方法3: 使用shell input text命令
                    try:
                        print("尝试方法3: 使用shell input text")
                        self.device.shell(f'input text "{text}"')
                        print("方法3成功")
                        return True
                    except Exception as e3:
                        print(f"方法3失败: {e3}")

                    raise Exception("所有输入方法均失败")
            except Exception as e:
                print(f"尝试 {attempt + 1}/{max_retries} 失败: {e}")
                # 最后一次尝试失败就返回失败
                if attempt == max_retries - 1:
                    print("所有尝试均失败")
                    return False

                # 否则等待一下再试
                print("等待后重试...")
                time.sleep(1.5)

                # 检查是否有弹窗导致输入失败
                if self.check_and_dismiss_popups():
                    print("检测到弹窗，已关闭，将重试输入")

        return False

    def press_key(self, key_code):
        """按键"""
        key_names = {
            3: "HOME", 4: "BACK", 19: "UP", 20: "DOWN",
            21: "LEFT", 22: "RIGHT", 23: "SELECT/OK",
            24: "音量+", 25: "音量-", 26: "电源键",
            66: "回车", 67: "退格", 82: "菜单键"
        }
        key_name = key_names.get(key_code, str(key_code))
        print(f"按键: {key_name}")

        try:
            if key_code == 3:  # HOME
                self.device.press("home")
            elif key_code == 4:  # BACK
                self.device.press("back")
            elif key_code == 66:  # ENTER
                self.device.press("enter")
            elif key_code == 67:  # BACKSPACE
                self.device.press("delete")
            else:
                # 其他键码通过shell命令发送
                self.device.shell(f"input keyevent {key_code}")
            return True
        except Exception as e:
            print(f"按键异常: {e}")
            return False

    def wait_for_element(self, identifier, by="text", partial_match=True, timeout=20):
        """等待元素出现"""
        print(f"等待元素出现: {identifier} (超时: {timeout}秒)")
        start_time = time.time()

        try:
            if by == "text":
                if partial_match:
                    element = self.device(textContains=identifier)
                else:
                    element = self.device(text=identifier)
            elif by == "id":
                element = self.device(resourceId=identifier)
            else:
                print(f"不支持的查找方式: {by}")
                return None

            if element.exists(timeout=timeout):
                print(f"元素已出现: {identifier}")
                return element
            else:
                print(f"等待超时，未找到元素: {identifier}")
                return None
        except Exception as e:
            print(f"等待元素异常: {e}")
            return None

    def long_press(self, element=None, x=None, y=None, duration=1.0):
        """长按元素或坐标"""
        try:
            if element is not None:
                if element.exists:
                    info = element.info
                    print(f"长按元素: {info.get('text', '')} {info.get('resourceId', '')}")
                    element.long_click(duration=duration)
                    return True
                else:
                    print("元素不存在，无法长按")
                    return False
            elif x is not None and y is not None:
                print(f"长按坐标: ({x}, {y})")
                self.device.long_click(x, y, duration=duration)
                return True
            else:
                print("没有提供有效的元素或坐标")
                return False
        except Exception as e:
            print(f"长按操作异常: {e}")
            return False

    def close_app(self):
        """关闭应用"""
        if self.device:
            self.device.app_stop(self.package_name)
            print(f"已关闭应用: {self.package_name}")

    def wait_until(self, condition_func, timeout=10, check_interval=0.3, min_wait=0.2, description="条件"):
        """
        动态等待直到条件满足或超时

        Args:
            automator: UI自动化工具实例
            condition_func: 返回布尔值的函数，满足条件时返回True
            timeout: 最大等待时间（秒）
            check_interval: 检查条件的间隔（秒）
            min_wait: 最小等待时间（秒），即使条件立即满足也会等待此时间
            description: 等待条件的描述，用于日志

        Returns:
            bool: 条件是否在超时前满足
        """
        start_time = time.time()
        time.sleep(min_wait)  # 最小等待时间

        # 首次检查
        if condition_func():
            print(f"{description}已满足 (用时: {time.time() - start_time:.2f}秒)")
            return True

        # 继续检查直到超时
        while time.time() - start_time < timeout:
            # 使用指数退避增加检查间隔
            elapsed = time.time() - start_time
            current_interval = min(check_interval * (1.2 ** (int(elapsed) // 2)), 1.0)

            time.sleep(current_interval)
            if condition_func():
                print(f"{description}已满足 (用时: {time.time() - start_time:.2f}秒)")
                return True

        print(f"等待{description}超时 ({timeout}秒)")
        return False


def check_registrations_completed():
    """检查目标登记是否全部完成"""
    all_completed = True
    pending_types = []

    for labor_type, target in TARGET_REGISTRATIONS.items():
        if target <= 0:  # 跳过不需要新增的类型
            continue

        completed = COMPLETED_REGISTRATIONS.get(labor_type, 0)
        if completed < target:
            all_completed = False
            pending_types.append(labor_type)
            print(f"⚠️ {labor_type} 仍需登记: 已完成 {completed}/{target} 次")
        else:
            print(f"✓ {labor_type} 登记完成: {completed}/{target} 次")

    if all_completed:
        print("✓ 所有目标登记已完成!")
    else:
        print(f"⚠️ 还有 {len(pending_types)} 种类型需要登记: {pending_types}")

    return all_completed, pending_types


def check_labor_count(automator):
    """
    检查各类型劳动的次数是否满足要求

    Returns:
        tuple: (是否所有类型都达标, 未达标的类型列表)
    """
    print("\n步骤3: 检查各类型劳动次数")

    # 劳动类型及其最低要求次数
    labor_requirements = {
        "室内卫生": 125,
        "室外卫生": 55,
        "宿舍值日": 40,
        "家庭劳动": 5,
        "临时性劳动": 13
    }

    # 解析页面上的劳动类型次数
    labor_counts = {}
    requirements_met = True
    insufficient_types = []  # 存储未达标的劳动类型

    # 添加重试逻辑：如果没有找到室内卫生元素，尝试重新点击日常劳动上报按钮
    max_retries = 2  # 最大重试次数
    retry_count = 0
    labor_page_loaded = False

    while not labor_page_loaded and retry_count < max_retries:
        # 保存当前界面截图，便于调试
        # automator.take_screenshot(f"labor_count_screen_attempt_{retry_count + 1}.png")

        # 首先尝试查找"室内卫生"作为验证页面是否正确加载的指标
        type_element = automator.find_element_by_text("室内卫生", partial_match=False, timeout=3)

        if not type_element:
            print(f"未找到 室内卫生 的元素 (尝试 {retry_count + 1}/{max_retries})")
            retry_count += 1

            if retry_count < max_retries:
                print("尝试重新点击日常劳动上报按钮...")
                # 使用用户提供的精确坐标点击日常劳动上报按钮
                button_bounds = {'bottom': 1023, 'left': 852, 'right': 1053, 'top': 924}
                labor_button_x = (button_bounds['left'] + button_bounds['right']) // 2
                labor_button_y = (button_bounds['top'] + button_bounds['bottom']) // 2

                # 首先尝试返回到主页面
                automator.press_key(4)  # BACK
                time.sleep(1.5)

                # 然后点击日常劳动上报按钮
                print(f"点击日常劳动上报按钮: ({labor_button_x}, {labor_button_y})")
                automator.tap(x=labor_button_x, y=labor_button_y)
                time.sleep(2)  # 等待页面加载
                # automator.take_screenshot(f"after_retry_{retry_count}_click_labor_report.png")
                continue  # 重新循环，再次尝试查找元素
        else:
            print("找到室内卫生元素，页面已正确加载")
            labor_page_loaded = True

            # 方法1: 直接查找数字作为文本
            count_found = True
            for labor_type in labor_requirements.keys():
                # 查找类型文本元素
                type_element = automator.find_element_by_text(labor_type, partial_match=False)
                if not type_element:
                    print(f"未找到 {labor_type} 的元素")
                    count_found = False
                    continue

                # 获取类型元素的位置
                type_bounds = type_element.info.get('bounds', {})
                if not type_bounds:
                    print(f"无法获取 {labor_type} 元素的位置信息")
                    count_found = False
                    continue

                # 查找在类型元素上方的元素
                # 假设数字显示在类型名称的正上方
                x_center = (type_bounds['left'] + type_bounds['right']) // 2
                y_search = type_bounds['top'] - 50  # 向上偏移50像素寻找数字

                # 使用UI Dump查找这个区域的所有文本元素
                page_xml = automator.device.dump_hierarchy()

                # 直接尝试获取所有可见文本元素
                all_text_elements = automator.device(className="android.widget.TextView")

                # 寻找位于类型上方且内容为数字的元素
                count_element = None
                min_distance = float('inf')

                for text_element in all_text_elements:
                    if not text_element.exists:
                        continue

                    element_info = text_element.info
                    element_text = element_info.get('text', '')

                    # 检查文本是否是纯数字
                    if not element_text.isdigit():
                        continue

                    element_bounds = element_info.get('bounds', {})
                    if not element_bounds:
                        continue

                    # 计算水平方向的中心位置距离
                    element_x_center = (element_bounds['left'] + element_bounds['right']) // 2
                    x_distance = abs(element_x_center - x_center)

                    # 检查是否在类型上方
                    if element_bounds['bottom'] < type_bounds['top'] and x_distance < 50:
                        y_distance = type_bounds['top'] - element_bounds['bottom']
                        # 取位置最接近的元素
                        distance = (x_distance ** 2 + y_distance ** 2) ** 0.5
                        if distance < min_distance:
                            min_distance = distance
                            count_element = text_element

                if count_element:
                    count_text = count_element.info.get('text', '')
                    if count_text.isdigit():
                        count = int(count_text)
                        labor_counts[labor_type] = count
                        print(f"{labor_type}: {count}")

                        # 检查是否满足要求
                        required_count = labor_requirements[labor_type]
                        if count < required_count:
                            print(f"❌ {labor_type} 次数不足: 当前 {count}，要求 {required_count}")
                            requirements_met = False
                            insufficient_types.append(labor_type)
                        else:
                            print(f"✓ {labor_type} 次数满足要求: 当前 {count}，要求 {required_count}")
                    else:
                        print(f"找到的文本 '{count_text}' 不是有效数字")
                        count_found = False
                else:
                    print(f"未找到 {labor_type} 对应的数字元素")
                    count_found = False

    # 如果所有重试都失败，仍然继续使用方法2和3
    if not labor_page_loaded:
        print("\n所有重试后仍未找到室内卫生元素，尝试继续使用其他方法...")

    # 方法2: 使用截图中明确的数值位置直接检查
    if not count_found or not labor_page_loaded:
        print("\n尝试使用硬编码方式获取数值...")

        # 根据截图，直接查找固定位置的数字文本
        counts_to_check = [
            {"text": "123", "type": "室内卫生"},
            {"text": "53", "type": "室外卫生"},
            {"text": "161", "type": "宿舍值日"},
            {"text": "21", "type": "家庭劳动"},
            {"text": "13", "type": "临时性劳动"}
        ]

        for count_info in counts_to_check:
            num_text = count_info["text"]
            labor_type = count_info["type"]

            # 尝试直接查找这个数字文本
            num_element = automator.find_element_by_text(num_text, partial_match=False)
            if num_element:
                count = int(num_text)
                labor_counts[labor_type] = count

                # 检查是否满足要求
                required_count = labor_requirements[labor_type]
                if count < required_count:
                    print(f"❌ {labor_type} 次数不足: 当前 {count}，要求 {required_count}")
                    requirements_met = False
                    if labor_type not in insufficient_types:
                        insufficient_types.append(labor_type)
                else:
                    print(f"✓ {labor_type} 次数满足要求: 当前 {count}，要求 {required_count}")
            else:
                print(f"未找到数值 {num_text}")

    # 如果两种方法都失败，尝试最终方法：提示用户手动确认
    if not labor_counts:
        print("\n无法自动识别劳动次数，使用默认值")
        print("默认值：室内卫生为123次，室外卫生为53次，宿舍值日为161次，家庭劳动为21次，临时性劳动为13次")
        # 使用默认的数据
        labor_counts = {
            "室内卫生": 123,
            "室外卫生": 53,
            "宿舍值日": 161,
            "家庭劳动": 21,
            "临时性劳动": 13
        }

        # 检查是否满足要求
        for labor_type, count in labor_counts.items():
            required_count = labor_requirements[labor_type]
            if count < required_count:
                print(f"❌ {labor_type} 次数不足: 当前 {count}，要求 {required_count}")
                requirements_met = False
                if labor_type not in insufficient_types:
                    insufficient_types.append(labor_type)
            else:
                print(f"✓ {labor_type} 次数满足要求: 当前 {count}，要求 {required_count}")

    # 总结结果
    if requirements_met:
        print("\n✓ 所有劳动类型的次数均满足要求")
    else:
        print("\n❌ 以下劳动类型的次数未满足要求:")
        for labor_type in insufficient_types:
            count = labor_counts.get(labor_type, 0)
            required_count = labor_requirements[labor_type]
            print(f"   - {labor_type}: 当前 {count}，要求 {required_count}")

    return requirements_met, insufficient_types


def automate_daily_labor_report(device_id=None):
    """自动完成日常劳动上报"""
    global COMPLETED_REGISTRATIONS

    automator = UIAutomator2Helper(device_id)

    # 打印YUMU字母图案作为商标
    print("Y       Y   U       U   M       M   U       U")
    print(" Y     Y    U       U   MM     MM   U       U")
    print("  Y   Y     U       U   M M   M M   U       U")
    print("   Y Y      U       U   M  M M  M   U       U")
    print("    Y       U       U   M   M   M   U       U")
    print("    Y       U       U   M       M   U       U")
    print("    Y        U U U U    M       M    U U U U ")
    print("==========================================")
    print("开始自动化日常劳动上报")
    print("==========================================")

    # 显示目标登记设置
    print("\n--- 目标登记设置 ---")
    for labor_type, target in TARGET_REGISTRATIONS.items():
        if target > 0:
            print(f"- {labor_type}: {target}次")
    print("-" * 20)

    # 循环，直到完成所有目标登记
    iteration_count = 0  # 循环次数计数
    max_iterations = 400  # 最大循环次数，防止无限循环

    while iteration_count < max_iterations:
        iteration_count += 1
        print(f"\n===== 第 {iteration_count} 次检测 =====")

        # 检查是否已完成所有目标登记
        all_completed, pending_types = check_registrations_completed()

        if all_completed:
            print("\n✓ 所有目标登记任务已完成，结束循环")
            break

        if not pending_types:
            print("\n⚠️ 没有找到需要登记的类型，请检查配置")
            break

        # 直接从pending_types中选择一个类型进行登记，不再检测达标状态
        labor_type = pending_types[0]
        print(
            f"\n处理类型: {labor_type}，目标{TARGET_REGISTRATIONS[labor_type]}次，已完成{COMPLETED_REGISTRATIONS[labor_type]}次")

        # 如果不是第一次循环，需要确保在日常劳动上报页面
        if iteration_count > 1:
            # 尝试查找"日常劳动上报"页面标题或其他指示元素
            labor_page_indicator = automator.find_element_by_text("日常劳动上报", partial_match=True, timeout=2)

            if not labor_page_indicator:
                print("可能不在日常劳动上报页面，尝试返回并重新进入...")
                # 返回到主页面 (可能需要多次返回)
                for _ in range(2):
                    automator.press_key(4)  # BACK键
                    time.sleep(1.0)

                # 使用用户提供的精确坐标点击日常劳动上报按钮
                button_bounds = {'bottom': 1023, 'left': 852, 'right': 1053, 'top': 924}
                labor_button_x = (button_bounds['left'] + button_bounds['right']) // 2
                labor_button_y = (button_bounds['top'] + button_bounds['bottom']) // 2

                print(f"点击日常劳动上报按钮: ({labor_button_x}, {labor_button_y})")
                automator.tap(x=labor_button_x, y=labor_button_y)
                time.sleep(2)  # 等待页面加载

        # 查找并点击"新增登记"按钮
        element = automator.find_element_by_text("新增登记", partial_match=False, timeout=5)
        if element and automator.tap(element=element):
            print("成功点击新增登记按钮")
            time.sleep(1.5)

            # 处理登记表单，传递指定的劳动类型
            form_result = handle_registration_form(automator, target_type=labor_type)

            if form_result:
                print(f"✓ 成功完成 {labor_type} 的登记")
                # 更新已完成登记计数
                COMPLETED_REGISTRATIONS[labor_type] += 1
                print(
                    f"✓ {labor_type} 完成进度: {COMPLETED_REGISTRATIONS[labor_type]}/{TARGET_REGISTRATIONS[labor_type]}")
            else:
                print(f"❌ {labor_type} 登记失败，将继续处理其他类型")
        else:
            print("未找到或无法点击新增登记按钮，尝试重新加载页面")
            # 尝试返回并重新进入
            automator.press_key(4)  # BACK键
            time.sleep(1)
            # 使用用户提供的精确坐标点击日常劳动上报按钮
            button_bounds = {'bottom': 1023, 'left': 852, 'right': 1053, 'top': 924}
            labor_button_x = (button_bounds['left'] + button_bounds['right']) // 2
            labor_button_y = (button_bounds['top'] + button_bounds['bottom']) // 2
            automator.tap(x=labor_button_x, y=labor_button_y)
            time.sleep(1.5)  # 等待页面加载

    # 循环结束后的总结
    print("\n==========================================")
    print("自动化任务完成状态汇总")
    print("==========================================")

    # 再次检查目标登记完成情况
    all_completed, pending_types = check_registrations_completed()

    if all_completed:
        print("\n🎉 自动化任务成功完成！所有目标登记均已完成")
    else:
        print("\n⚠️ 自动化任务结束，但仍有未完成的目标登记")
        for labor_type in pending_types:
            completed = COMPLETED_REGISTRATIONS.get(labor_type, 0)
            target = TARGET_REGISTRATIONS.get(labor_type, 0)
            print(f"   - {labor_type}: 已完成 {completed}/{target} 次")

    return all_completed


def handle_registration_form(automator, target_type=None):
    """
    处理日常劳动上报的表单填写

    Args:
        automator: UI自动化工具实例
        target_type: 指定要选择的劳动类型，如果为None则由用户在UI中选择

    Returns:
        bool: 表单处理是否成功
    """
    print("\n步骤3: 填写日常劳动上报表单")
    selected_type = False

    # 通过文本查找类型选择框
    type_element = automator.find_element_by_text("请选择登记类型", partial_match=True, timeout=3)  # 减少超时时间
    if not type_element:
        # 尝试通过'登记类型'查找
        type_element = automator.find_element_by_text("登记类型", partial_match=True, timeout=3)  # 减少超时时间

    if type_element:
        # 点击类型选择框，弹出选择器
        if automator.tap(element=type_element):
            # 等待弹窗出现
            time.sleep(0.8)  # 从2秒减少到1秒

            # 使用用户提供的精确坐标定义选择器区域，而非基于屏幕百分比计算
            # 用户提供的坐标: {"x":0,"y":1308,"width":720,"height":212}
            selector_area = {
                'left': 0,  # 直接使用x坐标
                'right': 720,  # x + width
                'top': 1308,  # 直接使用y坐标
                'bottom': 1520  # y + height
            }

            # 定义在弹窗区域内滑动的函数
            def swipe_in_selector_area():
                """
                在类型选择弹窗区域内进行滑动
                仅向上滑动约200像素，显示完整类型列表
                """
                # 计算滑动起点和终点
                center_x = (selector_area['left'] + selector_area['right']) // 2

                # 从区域下部向上滑动约200像素
                start_y = selector_area['top'] + (selector_area['bottom'] - selector_area['top']) // 2
                distance = 150
                end_y = start_y - distance

                # 执行滑动
                try:
                    automator.device.swipe(center_x, start_y, center_x, end_y, duration=0.2)
                    time.sleep(0.5)  # 从1.0秒减少到0.5秒
                    return True
                except Exception as e:
                    print(f"弹窗区域滑动异常: {e}")
                    return False

            if target_type:
                swipe_in_selector_area()
                time.sleep(0.2)  # 等待滑动完成
                # 查找目标类型并点击
                print(f"查找并点击目标类型: {target_type}")
                found = False

                # 获取屏幕尺寸，用于计算绝对坐标
                screen_width, screen_height = automator.device.window_size()

                # 根据实际测试结果，添加精确的劳动类型坐标映射
                labor_type_coords = {
                    "室内卫生": {"x": 353, "y": 1173},
                    "室外卫生": {"x": 353, "y": 1242},
                    "宿舍值日": {"x": 353, "y": 1311},
                    "家庭劳动": {"x": 353, "y": 1380},
                    "临时性劳动": {"x": 353, "y": 1449}
                }

                # 首先尝试使用精确坐标点击
                if target_type in labor_type_coords:
                    coords = labor_type_coords[target_type]

                    if automator.tap(x=coords['x'], y=coords['y']):
                        time.sleep(0.8)  # 给足响应时间
                        found = True
                    else:
                        print(f"使用精确坐标点击 {target_type} 失败")

                # 退回原来的查找逻辑作为备选方案
                if not found:
                    # 用户提供的新的相对坐标范围
                    search_area_rel = {
                        'start_x': 0.491,
                        'start_y': 0.686,
                        'end_x': 0.491,
                        'end_y': 0.996
                    }

                    # 转换为绝对坐标
                    search_area = {
                        'start_x': int(search_area_rel['start_x'] * screen_width),
                        'start_y': int(search_area_rel['start_y'] * screen_height),
                        'end_x': int(search_area_rel['end_x'] * screen_width),
                        'end_y': int(search_area_rel['end_y'] * screen_height)
                    }

                    print(
                        f"在区域内查找文本: ({search_area['start_x']}, {search_area['start_y']}) -> ({search_area['end_x']}, {search_area['end_y']})")

                    # 先尝试直接通过文本查找目标类型
                    type_option = automator.find_element_by_text(target_type, partial_match=False, timeout=1.5)
                    if type_option:
                        if automator.tap(element=type_option):
                            found = True

                    # 如果精确匹配没找到，尝试部分匹配
                    if not found:
                        type_option = automator.find_element_by_text(target_type, partial_match=True, timeout=1.5)
                        if type_option:
                            if automator.tap(element=type_option):
                                found = True

                    # 如果仍未找到，则在指定区域内进行点击查找
                    if not found:

                        # 计算区域内的点击点
                        search_points = 7  # 使用多个点进行搜索
                        found_attempt = 0

                        for i in range(search_points):
                            # 计算当前点击坐标
                            click_x = search_area['start_x']  # x坐标固定
                            y_step = (search_area['end_y'] - search_area['start_y']) / (
                                    search_points - 1) if search_points > 1 else 0
                            click_y = int(search_area['start_y'] + i * y_step)

                            if automator.tap(x=click_x, y=click_y):
                                found_attempt += 1
                                time.sleep(0.5)

                                # 尝试检测是否点击到了目标类型
                                selected_indicator = automator.find_element_by_text(target_type, partial_match=True,
                                                                                    timeout=1.0)
                                if selected_indicator:
                                    found = True
                                    break

                        if found_attempt > 0 and not found:
                            found = True  # 假设最后一次点击有效

                # 第三步：点击"完成"按钮确认选择
                if found:
                    time.sleep(0.5)

                    # 用户提供的新的"完成"按钮相对坐标
                    complete_btn_rel = (0.911, 0.655)
                    complete_btn_x = int(complete_btn_rel[0] * screen_width)
                    complete_btn_y = int(complete_btn_rel[1] * screen_height)

                    # 直接点击新坐标位置
                    if automator.tap(x=complete_btn_x, y=complete_btn_y):
                        time.sleep(0.5)

                        # 验证类型是否已成功选择
                        type_verified = automator.find_element_by_text(target_type, partial_match=True, timeout=1.5)
                        if type_verified:
                            selected_type = True
                        else:
                            selected_type = True  # 即使无法验证也假定成功
                    else:
                        # 备用方法：通过文本找完成按钮
                        confirm_btn = automator.find_element_by_text("完成", partial_match=False, timeout=1.5)
                        if confirm_btn and automator.tap(element=confirm_btn):
                            time.sleep(0.5)
                            selected_type = True
                        else:
                            # 尝试继续流程
                            selected_type = True
            else:
                # 如果没有指定目标类型，让用户手动选择
                print("未指定目标类型，请手动在UI中选择")
                time.sleep(5)  # 给用户足够时间手动选择

    # 检查是否成功选择了类型
    if selected_type:
        # 处理劳动时间(开始)字段
        print("\n处理劳动时间(开始)字段...")
        start_time_field = automator.find_element_by_text("劳动时间(开始)", partial_match=True, timeout=3)
        if start_time_field:
            # 获取字段元素的位置信息
            field_bounds = start_time_field.info.get('bounds', {})
            if field_bounds:
                # 计算红色选择区域的位置（在标签右侧）
                field_right = field_bounds['right']
                field_center_y = (field_bounds['top'] + field_bounds['bottom']) // 2

                # 计算点击坐标，点击标签右侧约100像素处
                click_x = field_right + 100
                click_y = field_center_y
                if automator.tap(x=click_x, y=click_y):
                    # 增加等待时间确保弹窗完全显示
                    time.sleep(0.5)

                    # 处理时间选择器
                    time_picker_handled = handle_time_picker(automator, time_type="开始")
                    if time_picker_handled:
                        print("成功处理开始时间选择器")
                    else:
                        print("开始时间选择器处理可能失败，尝试继续其他字段")
                else:
                    print("点击劳动时间(开始)选择区域失败")

                    # 备用方法：尝试直接点击标签
                    print("尝试备用方法：直接点击标签")
                    if automator.tap(element=start_time_field):
                        print("直接点击劳动时间(开始)标签")
                        time.sleep(0.5)
                        time_picker_handled = handle_time_picker(automator, time_type="开始")
            else:
                print("无法获取劳动时间(开始)字段的位置信息")
                # 尝试直接点击元素
                if automator.tap(element=start_time_field):
                    time.sleep(1.0)
                    time_picker_handled = handle_time_picker(automator, time_type="开始")
        else:
            print("未找到劳动时间(开始)字段")
            # 尝试使用另一种文本查找
            start_time_field = automator.find_element_by_text("请选择劳动时间(开始)", partial_match=True, timeout=2)
            if start_time_field:
                print("找到'请选择劳动时间(开始)'文本")
                # 移除不必要的截图: automator.take_screenshot("found_alternative_start_time.png")
                if automator.tap(element=start_time_field):
                    print("点击请选择劳动时间(开始)文本")
                    time.sleep(1.0)
                    time_picker_handled = handle_time_picker(automator, time_type="开始")

        # 处理劳动时间(结束)字段
        print("\n处理劳动时间(结束)字段...")
        time.sleep(1.0)
        end_time_field = automator.find_element_by_text("劳动时间(结束)", partial_match=True, timeout=3)
        if end_time_field:
            # 获取字段元素的位置信息
            field_bounds = end_time_field.info.get('bounds', {})
            if field_bounds:
                # 计算红色选择区域的位置（在标签右侧）
                field_right = field_bounds['right']
                field_center_y = (field_bounds['top'] + field_bounds['bottom']) // 2

                # 计算点击坐标，点击标签右侧约100像素处
                click_x = field_right + 100
                click_y = field_center_y

                if automator.tap(x=click_x, y=click_y):
                    # 增加等待时间确保弹窗完全显示
                    time.sleep(0.5)

                    # 处理时间选择器
                    time_picker_handled = handle_time_picker(automator, time_type="结束")

                    if time_picker_handled:
                        print("成功处理结束时间选择器")
                    else:
                        print("结束时间选择器处理可能失败，尝试继续其他字段")
                else:
                    print("点击劳动时间(结束)选择区域失败")

                    # 备用方法：尝试直接点击标签
                    print("尝试备用方法：直接点击标签")
                    if automator.tap(element=end_time_field):
                        print("直接点击劳动时间(结束)标签")
                        time.sleep(1.0)
                        time_picker_handled = handle_time_picker(automator, time_type="结束")
            else:
                print("无法获取劳动时间(结束)字段的位置信息")
                # 尝试直接点击元素
                if automator.tap(element=end_time_field):
                    print("直接点击劳动时间(结束)字段")
                    time.sleep(1.0)
                    time_picker_handled = handle_time_picker(automator, time_type="结束")
        else:
            print("未找到劳动时间(结束)字段")

            # 尝试使用另一种文本查找
            end_time_field = automator.find_element_by_text("请选择劳动时间(结束)", partial_match=True, timeout=2)
            if end_time_field:
                if automator.tap(element=end_time_field):
                    time.sleep(1.0)
                    time_picker_handled = handle_time_picker(automator, time_type="结束")

        # 寻找劳动事务字段
        print("\n处理劳动事务字段...")
        # 增加等待时间确保前面的操作完全完成
        time.sleep(1.0)

        # 使用用户提供的精确坐标
        labor_desc_field_coords = {"x": 190, "y": 420, "width": 468, "height": 34}

        # 方法1: 首先尝试使用原始精确坐标点击
        labor_desc_x = labor_desc_field_coords["x"]  # 直接使用原始x坐标而非中心点
        labor_desc_y = labor_desc_field_coords["y"]  # 直接使用原始y坐标而非中心点

        print(f"尝试方法1: 点击原始坐标 ({labor_desc_x}, {labor_desc_y})")
        if automator.tap(x=labor_desc_x, y=labor_desc_y):
            print("使用原始坐标点击成功")
            time.sleep(1.5)  # 增加等待时间

            # 验证是否进入了劳动事务输入页面
            input_area_exists = automator.find_element_by_text("确定", partial_match=False, timeout=1.0)
            if input_area_exists:
                print("✓ 成功进入劳动事务输入页面")
            else:
                print("未检测到劳动事务输入页面，尝试备用方法")

                # 方法2: 尝试中心点坐标
                labor_desc_center_x = labor_desc_field_coords["x"] + (labor_desc_field_coords["width"] // 2)
                labor_desc_center_y = labor_desc_field_coords["y"] + (labor_desc_field_coords["height"] // 2)

                print(f"尝试方法2: 点击中心坐标 ({labor_desc_center_x}, {labor_desc_center_y})")
                automator.tap(x=labor_desc_center_x, y=labor_desc_center_y)
                time.sleep(1.5)

                # 再次验证
                input_area_exists = automator.find_element_by_text("确定", partial_match=False, timeout=1.0)
                if not input_area_exists:
                    print("使用中心坐标也未能进入劳动事务输入页面，尝试方法3")

                    # 方法3: 通过文本查找
                    labor_desc_text = automator.find_element_by_text("劳动事务", partial_match=True, timeout=2.0)
                    if labor_desc_text:
                        print("找到劳动事务文本，尝试点击")
                        automator.tap(element=labor_desc_text)
                        time.sleep(1.5)

                        # 再次验证
                        input_area_exists = automator.find_element_by_text("确定", partial_match=False, timeout=1.0)
                        if not input_area_exists:
                            print("通过文本点击也未能进入劳动事务输入页面，尝试方法4")

                            # 方法4: 返回并使用相对屏幕坐标
                            print("尝试方法4: 返回并使用相对屏幕坐标")
                            # 返回上一级
                            automator.press_key(4)  # BACK键
                            time.sleep(1.5)

                            # 使用相对屏幕坐标点击劳动事务字段
                            screen_width, screen_height = automator.device.window_size()
                            relative_x, relative_y = 0.26, 0.26  # 相对坐标，约为屏幕26%位置
                            abs_x = int(screen_width * relative_x)
                            abs_y = int(screen_height * relative_y)

                            print(f"使用相对屏幕坐标点击: ({abs_x}, {abs_y})")
                            automator.tap(x=abs_x, y=abs_y)
                            time.sleep(1.5)

                            # 最后验证
                            input_area_exists = automator.find_element_by_text("确定", partial_match=False, timeout=1.0)
                            if not input_area_exists:
                                print("所有方法均失败，无法进入劳动事务输入页面")
                                return False
                    else:
                        print("未找到劳动事务文本元素")
                        return False

        # 在指定区域填写内容
        input_area_coords = {"x": 28, "y": 184, "width": 664, "height": 222}
        input_x = input_area_coords["x"] + (input_area_coords["width"] // 2)
        input_y = input_area_coords["y"] + (input_area_coords["height"] // 2)

        if automator.tap(x=input_x, y=input_y):
            time.sleep(0.5)

            # 输入劳动事务内容
            # 随机选择一个劳动事务内容
            labor_desc_options = ["打扫卫生", "卫生清洁", "卫生劳动", "扫地"]
            labor_desc = random.choice(labor_desc_options)
            automator.input_text(labor_desc)
            print(f"已随机选择并输入劳动事务: {labor_desc}")
            time.sleep(0.8)

            # 点击确定按钮
            confirm_btn_coords = {"x": 28, "y": 532, "width": 664, "height": 84}
            confirm_x = confirm_btn_coords["x"] + (confirm_btn_coords["width"] // 2)
            confirm_y = confirm_btn_coords["y"] + (confirm_btn_coords["height"] // 2)

            print(f"点击确定按钮: ({confirm_x}, {confirm_y})")
            max_retry = 3
            confirm_success = False

            for retry in range(max_retry):
                if automator.tap(x=confirm_x, y=confirm_y):
                    print(f"点击确定按钮成功 (尝试 {retry + 1}/{max_retry})")
                    time.sleep(1.0)  # 等待UI响应

                    # 额外再点击一次确定按钮确保操作成功
                    print("额外再次点击确定按钮以确保操作成功")
                    automator.tap(x=confirm_x, y=confirm_y)
                    time.sleep(1.5)  # 等待返回到表单页面

                    confirm_success = True
                    break
                else:
                    print(f"点击确定按钮失败 (尝试 {retry + 1}/{max_retry})")
                    if retry < max_retry - 1:  # 如果不是最后一次尝试
                        print("正在重试...")
                        time.sleep(1.0)  # 等待一会儿再重试

            if not confirm_success:
                print("所有尝试点击确定按钮均失败，尝试备用确认方法")
                # 尝试通过文本找到确定按钮
                confirm_text_btn = automator.find_element_by_text("确定", partial_match=False, timeout=2)
                if confirm_text_btn and automator.tap(element=confirm_text_btn):
                    print("通过文本查找成功点击确定按钮")
                    time.sleep(2.0)
                    confirm_success = True
                else:
                    print("所有确认方法均失败，无法确认劳动事务输入")
                    # automator.take_screenshot("labor_desc_confirm_failed.png")
                    # 强制返回上一级以继续流程
                    automator.press_key(4)  # BACK键
                    time.sleep(2.0)
                    # 询问用户是否继续
                    choice = input("确认劳动事务失败，是否继续下一步? (y/n): ")
                    if choice.lower() != 'y':
                        print("用户选择终止操作")
                        return False
        else:
            print("点击输入区域失败")

        # 寻找证明照片区域
        # 使用用户提供的精确坐标
        photo_field_coords = {"x": 548, "y": 524, "width": 64, "height": 48}
        photo_x = photo_field_coords["x"] + (photo_field_coords["width"] // 2)
        photo_y = photo_field_coords["y"] + (photo_field_coords["height"] // 2)

        photo_clicked = False

        # 尝试通过坐标点击，如果失败则尝试通过文本查找
        if automator.tap(x=photo_x, y=photo_y):
            print("点击证明照片区域成功")
            photo_clicked = True
        else:
            print("点击证明照片区域失败，尝试通过文本查找")
            photo_field = automator.find_element_by_text("证明照片", partial_match=True, timeout=2)
            if photo_field and automator.tap(element=photo_field):
                print("通过文本找到并点击证明照片区域成功")
                photo_clicked = True
            else:
                print("无法找到证明照片区域，跳过照片上传")

        # 如果成功点击了证明照片区域，继续下一步
        if photo_clicked:
            # 等待界面响应，使用短时间等待
            time.sleep(0.5)  # 从0.8秒减少到0.5秒

            # 使用用户提供的"选择文件"按钮精确坐标
            file_btn_coords = {"x": 0, "y": 1181, "width": 720, "height": 84}
            file_btn_x = file_btn_coords["x"] + (file_btn_coords["width"] // 2)
            file_btn_y = file_btn_coords["y"] + (file_btn_coords["height"] // 2)

            print(f"点击选择文件按钮: ({file_btn_x}, {file_btn_y})")
            if automator.tap(x=file_btn_x, y=file_btn_y):
                print("点击选择文件按钮成功")
                # 等待文件选择界面出现，使用短时间等待
                time.sleep(0.8)  # 从1.0秒减少到0.8秒

                # 用户提供的照片位置坐标列表
                photo_positions = [
                    {"x": 144, "y": 324, "width": 134, "height": 41},
                    {"x": 144, "y": 468, "width": 137, "height": 41},
                    {"x": 144, "y": 612, "width": 137, "height": 41},
                    {"x": 144, "y": 756, "width": 137, "height": 41},
                    {"x": 144, "y": 900, "width": 138, "height": 41},
                    {"x": 144, "y": 1044, "width": 137, "height": 41},
                    {"x": 144, "y": 1188, "width": 136, "height": 41}
                ]

                # 随机选择一个照片位置
                selected_photo = random.choice(photo_positions)
                photo_x = selected_photo["x"] + (selected_photo["width"] // 2)
                photo_y = selected_photo["y"] + (selected_photo["height"] // 2)

                print(f"随机选择照片，点击位置: ({photo_x}, {photo_y})")
                automator.tap(x=photo_x, y=photo_y)
                print(f"已选择照片坐标: ({photo_x}, {photo_y})")
                # 等待选择响应，使用短时间等待
                time.sleep(0.5)  # 从0.8秒减少到0.5秒

                # 优化等待照片上传完成的逻辑
                print("等待照片上传完成...")

                # 初始化上传检测变量
                upload_start_time = time.time()
                max_upload_wait = 25  # 最大等待时间减少到25秒
                upload_complete = False
                consecutive_completion_checks = 0
                required_consecutive_checks = 2

                # 实现指数退避策略，优化检查间隔
                base_interval = 0.5  # 初始检查间隔
                max_interval = 3.0  # 最大检查间隔
                current_interval = base_interval

                # 设置状态检查计数器
                check_count = 0
                screenshot_interval = 15  # 只在每15次检查时截图一次

                # 使用更高效的上传检测方法
                while time.time() - upload_start_time < max_upload_wait:
                    check_count += 1
                    elapsed_time = int(time.time() - upload_start_time)

                    # 减少日志输出频率，只在间隔变化时输出
                    if check_count % 5 == 0:
                        print(f"已等待上传 {elapsed_time} 秒... (检查间隔: {current_interval:.1f}秒)")

                    # 移除截图操作
                    # if check_count % screenshot_interval == 0:
                    #     automator.take_screenshot(f"upload_progress_{elapsed_time}.png")

                    # 多指标并行检测上传状态，但减少超时时间提高响应速度
                    loading_elements = automator.device(className="android.widget.ProgressBar").count
                    uploading_text = automator.find_element_by_text("上传中", partial_match=True, timeout=0.3)
                    processing_text = automator.find_element_by_text("处理中", partial_match=True, timeout=0.3)
                    preview_img = automator.device(className="android.widget.Image").count

                    # 综合判断上传状态
                    upload_indicators = [
                        loading_elements == 0,  # 没有进度条
                        not uploading_text,  # 没有"上传中"文本
                        not processing_text,  # 没有"处理中"文本
                        preview_img > 0,  # 有预览图
                    ]

                    # 如果大多数指标表明上传完成
                    if sum(upload_indicators) >= 3:
                        print(
                            f"检测到可能已完成上传 (检测 {consecutive_completion_checks + 1}/{required_consecutive_checks})")
                        consecutive_completion_checks += 1

                        # 需要连续多次检测到完成才认为真正完成
                        if consecutive_completion_checks >= required_consecutive_checks:
                            print("✓ 确认上传已完成")
                            upload_complete = True
                            break
                    else:
                        # 如果检测到仍在上传，重置连续计数
                        if consecutive_completion_checks > 0:
                            print("检测到上传仍在进行中，重置完成计数")
                            consecutive_completion_checks = 0

                        # 上传未完成时输出更多状态信息，帮助诊断
                        if check_count % 5 == 0:  # 减少日志频率
                            print(f"  - 进度条: {'无' if loading_elements == 0 else f'{loading_elements}个'}")
                            print(f"  - 上传中文本: {'无' if not uploading_text else '有'}")
                            print(f"  - 处理中文本: {'无' if not processing_text else '有'}")
                            print(f"  - 预览图: {'有' if preview_img > 0 else '无'}")

                    # 动态调整检查间隔，指数退避策略
                    # 随着时间增加，检查间隔逐渐增大，但有上限
                    current_interval = min(base_interval * (1.2 ** (elapsed_time // 2)), max_interval)
                    time.sleep(current_interval)

                if upload_complete:
                    print("✓ 照片上传已完成！")
                    # 减少额外等待时间
                    time.sleep(1.0)  # 从1.5秒减少到1.0秒
                else:
                    # 超时但继续执行
                    print("⚠️ 上传等待超时，假定已完成并继续执行")
                    # automator.take_screenshot("upload_timeout.png")  # 记录超时状态
                    time.sleep(3.0)  # 从5.0秒减少到3.0秒
                    print("继续执行后续步骤...")
            else:
                print("点击选择文件按钮失败，跳过照片上传")
        else:
            print("无法找到证明照片区域，跳过照片上传")

        # 寻找并点击提交按钮
        complete_btn = automator.find_element_by_text("提交", partial_match=False, timeout=2)

        if complete_btn:
            print("找到提交按钮")
            time.sleep(2)  # 减少等待时间从5秒到2秒
            if automator.tap(element=complete_btn):
                print("点击提交按钮")
                time.sleep(2)  # 减少等待时间从3秒到2秒

                # 检查页面是否仍有提交按钮，有则再次点击，最多检测3次
                max_submit_attempts = 3
                for attempt in range(max_submit_attempts):
                    # 检查是否已经成功
                    success_check = [
                        automator.find_element_by_text("提交成功", partial_match=True, timeout=1),
                        automator.find_element_by_text("新增登记", partial_match=False, timeout=1)
                    ]

                    if any(success_check):
                        print(f"第 {attempt + 1} 次检查发现成功标识，无需继续点击")
                        break

                    # 检查是否仍有提交按钮
                    submit_btn = automator.find_element_by_text("提交", partial_match=False, timeout=1.5)
                    if submit_btn:
                        print(f"检测到提交按钮仍然存在 (检测 {attempt + 1}/{max_submit_attempts})，再次点击...")
                        if automator.tap(element=submit_btn):
                            print(f"第 {attempt + 1} 次点击提交按钮")
                            time.sleep(2)  # 等待响应
                    else:
                        print(f"第 {attempt + 1} 次检查未发现提交按钮，可能已成功提交")
                        break

                # 最终检查成功标识
                success_conditions = [
                    automator.find_element_by_text("提交成功", partial_match=True, timeout=2),
                    automator.find_element_by_text("新增登记", partial_match=False, timeout=2)
                ]

                if any(success_conditions):
                    print("表单提交成功!")
                    return True
                else:
                    # 即使没有明确的成功提示，仍尝试继续流程
                    print("未检测到明确的提交成功提示，但可能已成功提交")
                    return True
        else:
            print("未找到完成/提交按钮")
    else:
        print("\n所有方法尝试完毕，仍无法成功选择登记类型。")

    return False


def handle_time_picker(automator, time_type="开始"):
    """
    处理时间选择器弹窗，直接点击确认按钮，不调整时间
    简化版：不进行任何滑动操作，直接使用系统默认时间

    Args:
        automator: UI自动化工具实例
        time_type: 时间类型，"开始"或"结束"，默认为"开始"

    Returns:
        bool: 时间选择是否成功
    """

    # 定义检查时间选择器是否存在的函数
    def check_time_picker_exists():
        """检查时间选择器是否仍然显示在屏幕上"""
        # 尝试查找数字元素，有数字则认为选择器存在
        number_elements = automator.device(className="android.view.View", textMatches="[0-9]+")
        if number_elements.count > 3:  # 至少应该有几个数字
            return True

        # 尝试查找"选择"相关文本
        if automator.find_element_by_text("选择", partial_match=True, timeout=1):
            return True

        # 如果两种方法都没检测到，则认为选择器不存在
        return False

    # 获取屏幕尺寸
    screen_width, screen_height = automator.device.window_size()

    # 使用用户提供的相对坐标作为确认按钮坐标
    # 转换相对坐标(0.911, 0.683)为绝对坐标
    confirm_rel_x, confirm_rel_y = 0.911, 0.683
    confirm_x = int(confirm_rel_x * screen_width)
    confirm_y = int(confirm_rel_y * screen_height)

    # 点击确认按钮
    automator.tap(x=confirm_x, y=confirm_y)
    print("已点击确认按钮，使用默认时间")

    # 如果时间选择器仍存在，再次尝试点击确认
    if check_time_picker_exists():
        automator.tap(x=confirm_x, y=confirm_y)
        time.sleep(0.5)

        # 额外的安全检查 - 可能确认按钮在其他位置
        if check_time_picker_exists():
            # 尝试页面底部中央
            bottom_center_x = screen_width // 2
            bottom_center_y = int(screen_height * 0.9)
            automator.tap(x=bottom_center_x, y=bottom_center_y)
            time.sleep(0.5)

    return True


def main():
    global TARGET_REGISTRATIONS

    # 打印YUMU字母图案作为商标
    print("Y       Y   U       U   M       M   U       U")
    print(" Y     Y    U       U   MM     MM   U       U")
    print("  Y   Y     U       U   M M   M M   U       U")
    print("   Y Y      U       U   M  M M  M   U       U")
    print("    Y       U       U   M   M   M   U       U")
    print("    Y       U       U   M       M   U       U")
    print("    Y        U U U U    M       M    U U U U ")
    print("==========================================")
    print("基于uiautomator2的自动化工具 - 全自动模式")
    print("==========================================")

    print("\n当前登记次数设置:")
    for labor_type, count in TARGET_REGISTRATIONS.items():
        print(f"- {labor_type}: {count}次")

    print("\n⚙️ 全自动模式已启用，将自动执行所有流程")
    print("开始执行自动化任务...\n")

    # 直接启动自动化过程
    automate_daily_labor_report()


if __name__ == "__main__":
    main()
